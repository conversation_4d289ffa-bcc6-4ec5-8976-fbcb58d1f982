"use client";

import { useState, useCallback } from "react";
import { useEnhancedFingerprint } from "@/hooks/use-enhanced-fingerprint";
import { useDeviceInfo } from "@/hooks/use-device-info";
import { useWebRTCInfo } from "@/hooks/use-webrtc-info";
import { FingerprintDiagnostics } from "@/components/fingerprint-diagnostics";
import {
  Globe,
  MapPin,
  Activity,
  Eye,
  Server,
  Copy,
  CheckCircle,
  Shield,
  Monitor,
  Clock,
  Download,
  Gauge,
  RefreshCw,
  AlertTriangle,
  Loader2,
  Signal,
  Smartphone
} from "lucide-react";

export default function ModernNetworkDetection() {
  const [copied, setCopied] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const enhancedFingerprint = useEnhancedFingerprint();
  const deviceInfo = useDeviceInfo();
  const webrtcInfo = useWebRTCInfo();

  // 安全渲染函数，确保不会直接渲染对象
  const safeRender = (value: any, fallback: string = '未知'): string => {
    try {
      if (value === null || value === undefined) return fallback;

      if (typeof value === 'object') {
        // 如果是对象，尝试获取常见的属性
        if (value.score !== undefined) return String(value.score);
        if (value.name !== undefined) return String(value.name);
        if (value.value !== undefined) return String(value.value);
        if (value.message !== undefined) return String(value.message);
        if (value.toString && typeof value.toString === 'function') {
          const stringValue = value.toString();
          if (stringValue !== '[object Object]') return stringValue;
        }
        // 如果是数组，转换为字符串
        if (Array.isArray(value)) return value.join(', ');
        return fallback;
      }

      return String(value);
    } catch (error) {
      console.warn('safeRender error:', error, 'value:', value);
      return fallback;
    }
  };


  // 刷新所有数据
  const refreshAllData = useCallback(async () => {
    setRefreshing(true);
    try {
      await enhancedFingerprint.refresh();
    } finally {
      setRefreshing(false);
    }
  }, [enhancedFingerprint]);


  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  // 计算网络质量评分
  const getNetworkQuality = useCallback(() => {
    if (!webrtcInfo?.connection) return { score: 0, level: 'unknown' as const };

    const latency = webrtcInfo.connection.rtt || 0;
    const effectiveType = webrtcInfo.connection.effectiveType;
    const downlink = webrtcInfo.connection.downlink || 0;

    let score = 100;

    // 基于延迟评分
    if (latency > 200) score -= 40;
    else if (latency > 100) score -= 25;
    else if (latency > 50) score -= 10;

    // 基于连接类型评分
    if (effectiveType === 'slow-2g') score -= 50;
    else if (effectiveType === '2g') score -= 35;
    else if (effectiveType === '3g') score -= 20;
    else if (effectiveType === '4g') score -= 5;

    // 基于下载速度评分
    if (downlink < 0.5) score -= 30;
    else if (downlink < 1) score -= 20;
    else if (downlink < 2) score -= 10;

    const finalScore = Math.max(score, 0);

    let level: 'excellent' | 'good' | 'fair' | 'poor' | 'unknown';
    if (finalScore >= 90) level = 'excellent';
    else if (finalScore >= 70) level = 'good';
    else if (finalScore >= 50) level = 'fair';
    else level = 'poor';

    return { score: finalScore, level };
  }, [webrtcInfo]);

  const networkQuality = getNetworkQuality();

  // 获取网络质量颜色
  const getQualityColor = (level: string) => {
    switch (level) {
      case 'excellent': return 'text-green-400';
      case 'good': return 'text-blue-400';
      case 'fair': return 'text-yellow-400';
      case 'poor': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  // 获取网络质量文本
  const getQualityText = (level: string) => {
    switch (level) {
      case 'excellent': return '优秀';
      case 'good': return '良好';
      case 'fair': return '一般';
      case 'poor': return '较差';
      default: return '未知';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
      {/* 顶部导航栏 */}
      <header className="border-b border-white/10 backdrop-blur-sm bg-black/20">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Shield className="h-8 w-8 text-blue-400" />
              <h1 className="text-2xl font-bold">网络检测中心</h1>
            </div>
            <div className="flex items-center gap-4">
              <button
                onClick={refreshAllData}
                disabled={refreshing}
                className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-white/10 hover:bg-white/20 transition-colors disabled:opacity-50"
                title="刷新所有数据"
              >
                <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                <span className="text-sm">刷新</span>
              </button>
              <div className="text-sm text-gray-400">
                <Clock className="h-4 w-4 inline mr-1" />
                <span suppressHydrationWarning>
                  {new Date().toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* IP 地址主显示区域 */}
        <div className="text-center mb-12">
          <div className="mb-4">
            <h2 className="text-lg text-gray-400 mb-2">我的IP地址</h2>
            <div className="flex items-center justify-center gap-4">
              {enhancedFingerprint.data.isLoading ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-6 w-6 animate-spin text-blue-400" />
                  <span className="ip-display text-gray-400">获取中...</span>
                </div>
              ) : enhancedFingerprint.data.clientData?.error ? (
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-6 w-6 text-red-400" />
                  <span className="text-red-400">获取失败</span>
                </div>
              ) : enhancedFingerprint.data.clientData?.result ? (
                <>
                  <span className="ip-display">
                    {safeRender(enhancedFingerprint.data.clientData.result.ip, "未知")}
                  </span>
                  {enhancedFingerprint.data.clientData.result.ip && (
                    <button
                      onClick={() => copyToClipboard(String(enhancedFingerprint.data.clientData!.result!.ip))}
                      className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                      title="复制IP地址"
                    >
                      {copied ? (
                        <CheckCircle className="h-5 w-5 text-green-400" />
                      ) : (
                        <Copy className="h-5 w-5" />
                      )}
                    </button>
                  )}
                </>
              ) : (
                <span className="ip-display text-gray-400">等待数据...</span>
              )}
            </div>
          </div>

          {enhancedFingerprint.data.clientData?.result?.ipLocation && (
            <div className="location-info justify-center">
              <MapPin className="h-5 w-5 text-blue-400" />
              <span>
                {safeRender(enhancedFingerprint.data.clientData.result.ipLocation.country?.name)}
                {enhancedFingerprint.data.clientData.result.ipLocation.subdivisions?.[0]?.name &&
                 ` / ${safeRender(enhancedFingerprint.data.clientData.result.ipLocation.subdivisions?.[0]?.name)}`}
              </span>
            </div>
          )}

          {enhancedFingerprint.data.clientData?.error && (
            <div className="mt-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="h-5 w-5 text-red-400" />
                <span className="text-red-400 font-medium">连接失败</span>
              </div>
              <p className="text-sm text-red-300 mb-3">
                {safeRender(enhancedFingerprint.data.clientData.error?.message, "未知错误")}
              </p>
              <div className="text-xs text-gray-400 mb-3">
                <p>可能的解决方案：</p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>检查网络连接是否正常</li>
                  <li>确认 Fingerprint Pro API 密钥有效</li>
                  <li>尝试刷新页面重新加载</li>
                  <li>检查防火墙设置是否阻止了 API 请求</li>
                </ul>
              </div>
              <button
                onClick={enhancedFingerprint.refresh}
                className="px-4 py-2 bg-red-500/20 text-red-400 rounded-lg hover:bg-red-500/30 transition-colors text-sm"
              >
                重试连接
              </button>
            </div>
          )}

          {/* API 诊断 - 仅在有错误时显示 */}
          {enhancedFingerprint.data.clientData?.error && (
            <FingerprintDiagnostics />
          )}
        </div>

        {/* 主要指标卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* 网络质量 */}
          <div className="network-card">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Signal className="h-5 w-5 text-blue-400" />
                <span className="font-medium">网络质量</span>
              </div>
              <span className={`status-indicator ${
                networkQuality.level === 'excellent' ? 'status-safe' :
                networkQuality.level === 'good' ? 'status-safe' :
                networkQuality.level === 'fair' ? 'status-warning' :
                networkQuality.level === 'poor' ? 'status-danger' : 'bg-gray-500/20 text-gray-400'
              }`}>
                {getQualityText(networkQuality.level)}
              </span>
            </div>

            <div className="relative w-24 h-24 mx-auto mb-4">
              <svg className="w-24 h-24 progress-ring">
                <circle
                  cx="48"
                  cy="48"
                  r="40"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="transparent"
                  className="text-gray-700"
                />
                <circle
                  cx="48"
                  cy="48"
                  r="40"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="transparent"
                  strokeDasharray={`${2 * Math.PI * 40}`}
                  strokeDashoffset={`${2 * Math.PI * 40 * (1 - (networkQuality.score || 0) / 100)}`}
                  className={`${getQualityColor(networkQuality.level)} transition-all duration-1000`}
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-bold">{Math.round(networkQuality.score || 0)}%</span>
              </div>
            </div>

            <div className="text-center text-sm text-gray-400">
              网络评分: {Math.round(networkQuality.score || 0)}/100
            </div>

            {webrtcInfo?.connection && (
              <div className="mt-3 text-xs text-gray-500 text-center">
                延迟: {webrtcInfo.connection.rtt ? `${safeRender(webrtcInfo.connection.rtt)}ms` : '--'}
              </div>
            )}
          </div>

          {/* 下载速度 */}
          <div className="network-card">
            <div className="flex items-center gap-2 mb-4">
              <Download className="h-5 w-5 text-blue-400" />
              <span className="font-medium">下载速度</span>
            </div>

            <div className="text-center">
              <div className="data-value-large text-blue-400 mb-2">
                {webrtcInfo?.connection?.downlink ?
                  `${safeRender(webrtcInfo.connection.downlink)} Mbps` :
                  '--'
                }
              </div>
              <div className="text-sm text-gray-400">
                有效类型: {safeRender(webrtcInfo?.connection?.effectiveType, '未知')}
              </div>
              {webrtcInfo?.connection?.saveData && (
                <div className="text-xs text-yellow-400 mt-1">
                  省流量模式
                </div>
              )}
            </div>
          </div>

          {/* 延迟 */}
          <div className="network-card">
            <div className="flex items-center gap-2 mb-4">
              <Gauge className="h-5 w-5 text-yellow-400" />
              <span className="font-medium">网络延迟</span>
            </div>

            <div className="text-center">
              <div className={`data-value-large mb-2 ${
                !webrtcInfo?.connection?.rtt ? 'text-gray-400' :
                webrtcInfo.connection.rtt < 50 ? 'text-green-400' :
                webrtcInfo.connection.rtt < 100 ? 'text-yellow-400' : 'text-red-400'
              }`}>
                {webrtcInfo?.connection?.rtt ?
                  `${safeRender(webrtcInfo.connection.rtt)} ms` :
                  '--'
                }
              </div>
              <div className="text-sm text-gray-400">
                往返时间
              </div>
              {webrtcInfo?.connection?.rtt && (
                <div className="text-xs text-gray-500 mt-1">
                  {webrtcInfo.connection.rtt < 50 ? '优秀' :
                   webrtcInfo.connection.rtt < 100 ? '良好' :
                   webrtcInfo.connection.rtt < 200 ? '一般' : '较差'}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 详细信息网格 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 设备信息 */}
          <div className="network-card">
            <h3 className="section-header">
              {deviceInfo?.deviceType === 'mobile' ? (
                <Smartphone className="h-6 w-6 text-purple-400" />
              ) : (
                <Monitor className="h-6 w-6 text-purple-400" />
              )}
              设备信息
            </h3>

            {deviceInfo ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="data-label">浏览器</div>
                    <div className="data-value">
                      {safeRender(deviceInfo.browser, '未知')} {safeRender(deviceInfo.browserVersion, '')}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">操作系统</div>
                    <div className="data-value">
                      {safeRender(deviceInfo.os, '未知')} {safeRender(deviceInfo.osVersion, '')}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">设备类型</div>
                    <div className="data-value">
                      {safeRender(deviceInfo.deviceType, '未知')}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">屏幕分辨率</div>
                    <div className="data-value">
                      {safeRender(deviceInfo.screenResolution, '未知')}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">像素比</div>
                    <div className="data-value">
                      {safeRender(deviceInfo.pixelRatio, '未知')}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">色彩深度</div>
                    <div className="data-value">
                      {deviceInfo.colorDepth ? `${safeRender(deviceInfo.colorDepth)}位` : '未知'}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="animate-pulse space-y-3">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-4 bg-gray-700 rounded"></div>
                ))}
              </div>
            )}
          </div>

          {/* 位置信息 */}
          <div className="network-card">
            <h3 className="section-header">
              <Globe className="h-6 w-6 text-green-400" />
              位置信息
              <span className="text-xs text-gray-500 ml-auto">
                来自 Fingerprint Pro
              </span>
            </h3>

            {enhancedFingerprint.data.isLoading ? (
              <div className="animate-pulse space-y-3">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-4 bg-gray-700 rounded"></div>
                ))}
              </div>
            ) : enhancedFingerprint.data.clientData?.error ? (
              <div className="text-center py-8">
                <AlertTriangle className="h-8 w-8 text-red-400 mx-auto mb-2" />
                <p className="text-red-400 mb-2">获取位置信息失败</p>
                <p className="text-sm text-gray-500">{safeRender(enhancedFingerprint.data.clientData.error?.message, "未知错误")}</p>
                <button
                  onClick={enhancedFingerprint.refresh}
                  className="mt-3 px-4 py-2 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors"
                >
                  重试
                </button>
              </div>
            ) : enhancedFingerprint.data.clientData?.result?.ipLocation ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="data-label">国家</div>
                    <div className="data-value">
                      {safeRender(enhancedFingerprint.data.clientData.result.ipLocation.country?.name, '未知')}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">地区</div>
                    <div className="data-value">
                      {safeRender(enhancedFingerprint.data.clientData.result.ipLocation.subdivisions?.[0]?.name, '未知')}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">城市</div>
                    <div className="data-value">
                      {safeRender(enhancedFingerprint.data.clientData.result.ipLocation.city?.name, '未知')}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">洲/大陆</div>
                    <div className="data-value">
                      {safeRender(enhancedFingerprint.data.clientData.result.ipLocation.continent?.name, '未知')}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">时区</div>
                    <div className="data-value">
                      {safeRender(enhancedFingerprint.data.clientData.result.ipLocation.timezone, '未知')}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">坐标</div>
                    <div className="data-value">
                      {enhancedFingerprint.data.clientData.result.ipLocation.latitude &&
                       enhancedFingerprint.data.clientData.result.ipLocation.longitude ?
                        `${safeRender(enhancedFingerprint.data.clientData.result.ipLocation.latitude.toFixed(4))}, ${safeRender(enhancedFingerprint.data.clientData.result.ipLocation.longitude.toFixed(4))}` :
                        '未知'
                      }
                    </div>
                  </div>
                  {enhancedFingerprint.data.clientData.result.ipLocation.postalCode && (
                    <div>
                      <div className="data-label">邮政编码</div>
                      <div className="data-value">
                        {String(enhancedFingerprint.data.clientData.result.ipLocation.postalCode)}
                      </div>
                    </div>
                  )}
                  {enhancedFingerprint.data.clientData.result.ipLocation.accuracyRadius && (
                    <div>
                      <div className="data-label">精度半径</div>
                      <div className="data-value">
                        {String(enhancedFingerprint.data.clientData.result.ipLocation.accuracyRadius)} km
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-400">
                暂无位置信息
              </div>
            )}
          </div>
        </div>

        {/* Fingerprint Pro 增强检测 */}
        {enhancedFingerprint.isAvailable && (
          <div className="mt-8">
            <div className="network-card">
              <div className="flex items-center justify-between mb-6">
                <h3 className="section-header">
                  <Shield className="h-6 w-6 text-blue-400" />
                  Fingerprint Pro 增强检测
                  {enhancedFingerprint.data.hasServerData && (
                    <span className="status-indicator status-info ml-4">
                      <Server className="h-3 w-3" />
                      服务端增强
                    </span>
                  )}
                </h3>
                <div className="flex gap-2">
                  {enhancedFingerprint.refreshServerData && enhancedFingerprint.data.hasServerData && (
                    <button
                      onClick={enhancedFingerprint.refreshServerData}
                      disabled={enhancedFingerprint.data.serverData?.isLoading}
                      className="p-1.5 rounded-md bg-white/10 hover:bg-white/20 transition-colors disabled:opacity-50"
                      title="刷新服务端数据"
                    >
                      <Server className={`h-4 w-4 ${enhancedFingerprint.data.serverData?.isLoading ? 'animate-spin' : ''}`} />
                    </button>
                  )}
                  <button
                    onClick={enhancedFingerprint.refresh}
                    disabled={enhancedFingerprint.data.isLoading}
                    className="p-1.5 rounded-md bg-white/10 hover:bg-white/20 transition-colors disabled:opacity-50"
                    title="刷新所有数据"
                  >
                    <RefreshCw className={`h-4 w-4 ${enhancedFingerprint.data.isLoading ? 'animate-spin' : ''}`} />
                  </button>
                </div>
              </div>

              {enhancedFingerprint.data.isLoading ? (
                <div className="animate-pulse space-y-3">
                  {[...Array(8)].map((_, i) => (
                    <div key={i} className="h-4 bg-gray-700 rounded"></div>
                  ))}
                </div>
              ) : enhancedFingerprint.data.clientData?.error ? (
                <div className="text-center py-8">
                  <AlertTriangle className="h-8 w-8 text-red-400 mx-auto mb-2" />
                  <p className="text-red-400 mb-2">Fingerprint Pro 数据获取失败</p>
                  <p className="text-sm text-gray-500">{safeRender(enhancedFingerprint.data.clientData.error?.message, "未知错误")}</p>
                  <button
                    onClick={enhancedFingerprint.refresh}
                    className="mt-3 px-4 py-2 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors"
                  >
                    重试
                  </button>
                </div>
              ) : enhancedFingerprint.data.clientData?.result ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* 基础识别 */}
                  <div>
                    <h4 className="font-medium text-gray-300 mb-3 flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      访问者识别
                    </h4>
                    <div className="space-y-2">
                      <div>
                        <div className="data-label">访问者ID</div>
                        <div className="data-value font-mono text-sm break-all">
                          {safeRender(enhancedFingerprint.data.clientData.result.visitorId)}
                        </div>
                      </div>
                      <div>
                        <div className="data-label">请求ID</div>
                        <div className="data-value font-mono text-xs text-gray-400 break-all">
                          {safeRender(enhancedFingerprint.data.clientData.result.requestId)}
                        </div>
                      </div>
                      <div>
                        <div className="data-label">置信度</div>
                        <div className={`data-value ${
                          enhancedFingerprint.data.clientData.result.confidence?.score >= 0.9 ? 'text-green-400' :
                          enhancedFingerprint.data.clientData.result.confidence?.score >= 0.7 ? 'text-yellow-400' :
                          'text-red-400'
                        }`}>
                          {enhancedFingerprint.data.clientData.result.confidence ?
                            `${(enhancedFingerprint.data.clientData.result.confidence.score * 100).toFixed(1)}%` :
                            '未知'
                          }
                        </div>
                      </div>
                      <div>
                        <div className="data-label">访问者状态</div>
                        <div className="data-value">
                          {enhancedFingerprint.data.clientData.result.visitorFound ? '已知访问者' : '新访问者'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 风险检测 */}
                  <div>
                    <h4 className="font-medium text-gray-300 mb-3 flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      风险检测
                      {!enhancedFingerprint.data.hasServerData && (
                        <span className="text-xs text-gray-500">(基础)</span>
                      )}
                    </h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="data-label">隐身模式</span>
                        <span className={`text-sm ${
                          enhancedFingerprint.data.clientData.result?.incognito ?
                          'text-yellow-400' : 'text-green-400'
                        }`}>
                          {enhancedFingerprint.data.clientData.result?.incognito ?
                           '是' : '否'}
                        </span>
                      </div>

                      {enhancedFingerprint.data.hasServerData ? (
                        <>
                          <div className="flex justify-between">
                            <span className="data-label">VPN</span>
                            <div className="flex items-center gap-1">
                              <span className={`text-sm ${
                                enhancedFingerprint.data.serverData?.currentVisit?.vpn?.result ?
                                'text-red-400' : 'text-green-400'
                              }`}>
                                {enhancedFingerprint.data.serverData?.currentVisit?.vpn?.result ?
                                 '检测到' : '未检测到'}
                              </span>
                              {enhancedFingerprint.data.serverData?.currentVisit?.vpn?.confidence && (
                                <span className="text-xs text-gray-500">
                                  ({typeof enhancedFingerprint.data.serverData.currentVisit.vpn.confidence === 'object'
                                    ? enhancedFingerprint.data.serverData.currentVisit.vpn.confidence.score || 'N/A'
                                    : String(enhancedFingerprint.data.serverData.currentVisit.vpn.confidence)})
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex justify-between">
                            <span className="data-label">代理</span>
                            <div className="flex items-center gap-1">
                              <span className={`text-sm ${
                                enhancedFingerprint.data.serverData?.currentVisit?.proxy?.result ?
                                'text-red-400' : 'text-green-400'
                              }`}>
                                {enhancedFingerprint.data.serverData?.currentVisit?.proxy?.result ?
                                 '检测到' : '未检测到'}
                              </span>
                              {enhancedFingerprint.data.serverData?.currentVisit?.proxy?.confidence && (
                                <span className="text-xs text-gray-500">
                                  ({typeof enhancedFingerprint.data.serverData.currentVisit.proxy.confidence === 'object'
                                    ? enhancedFingerprint.data.serverData.currentVisit.proxy.confidence.score || 'N/A'
                                    : String(enhancedFingerprint.data.serverData.currentVisit.proxy.confidence)})
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex justify-between">
                            <span className="data-label">Tor</span>
                            <div className="flex items-center gap-1">
                              <span className={`text-sm ${
                                enhancedFingerprint.data.serverData?.currentVisit?.tor?.result ?
                                'text-red-400' : 'text-green-400'
                              }`}>
                                {enhancedFingerprint.data.serverData?.currentVisit?.tor?.result ?
                                 '检测到' : '未检测到'}
                              </span>
                              {enhancedFingerprint.data.serverData?.currentVisit?.tor?.confidence && (
                                <span className="text-xs text-gray-500">
                                  ({typeof enhancedFingerprint.data.serverData.currentVisit.tor.confidence === 'object'
                                    ? enhancedFingerprint.data.serverData.currentVisit.tor.confidence.score || 'N/A'
                                    : String(enhancedFingerprint.data.serverData.currentVisit.tor.confidence)})
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex justify-between">
                            <span className="data-label">篡改检测</span>
                            <div className="flex items-center gap-1">
                              <span className={`text-sm ${
                                enhancedFingerprint.data.serverData?.currentVisit?.tampering?.result ?
                                'text-red-400' : 'text-green-400'
                              }`}>
                                {enhancedFingerprint.data.serverData?.currentVisit?.tampering?.result ?
                                 '检测到' : '未检测到'}
                              </span>
                              {enhancedFingerprint.data.serverData?.currentVisit?.tampering?.confidence && (
                                <span className="text-xs text-gray-500">
                                  ({typeof enhancedFingerprint.data.serverData.currentVisit.tampering.confidence === 'object'
                                    ? enhancedFingerprint.data.serverData.currentVisit.tampering.confidence.score || 'N/A'
                                    : String(enhancedFingerprint.data.serverData.currentVisit.tampering.confidence)})
                                </span>
                              )}
                            </div>
                          </div>
                          {enhancedFingerprint.data.serverData?.currentVisit?.suspectScore !== undefined && (
                            <div className="flex justify-between">
                              <span className="data-label">可疑评分</span>
                              <span className={`text-sm ${
                                (enhancedFingerprint.data.serverData.currentVisit.suspectScore || 0) > 50 ?
                                'text-red-400' : 'text-green-400'
                              }`}>
                                {safeRender(enhancedFingerprint.data.serverData.currentVisit.suspectScore || 0)}/100
                              </span>
                            </div>
                          )}
                        </>
                      ) : (
                        <div className="text-xs text-gray-500 italic">
                          服务端增强检测数据加载中...
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 访问历史 */}
                  <div>
                    <h4 className="font-medium text-gray-300 mb-3 flex items-center gap-2">
                      <Activity className="h-4 w-4" />
                      访问历史
                    </h4>
                    <div className="space-y-2">
                      <div>
                        <div className="data-label">访问次数</div>
                        <div className="data-value">
                          {safeRender(enhancedFingerprint.data.serverData?.visitorHistory?.visits?.length || 1)}
                        </div>
                      </div>
                      <div>
                        <div className="data-label">首次访问</div>
                        <div className="data-value text-sm">
                          {enhancedFingerprint.data.clientData.result.firstSeenAt?.global ?
                            safeRender(new Date(enhancedFingerprint.data.clientData.result.firstSeenAt.global).toLocaleString()) :
                            '刚刚'
                          }
                        </div>
                      </div>
                      <div>
                        <div className="data-label">最近访问</div>
                        <div className="data-value text-sm">
                          {enhancedFingerprint.data.clientData.result.lastSeenAt?.global ?
                            safeRender(new Date(enhancedFingerprint.data.clientData.result.lastSeenAt.global).toLocaleString()) :
                            '刚刚'
                          }
                        </div>
                      </div>

                      {enhancedFingerprint.data.serverData?.visitorHistory?.visits &&
                       (enhancedFingerprint.data.serverData.visitorHistory?.visits?.length || 0) > 1 && (
                        <div className="mt-3">
                          <div className="text-xs text-gray-500 mb-2">最近访问记录:</div>
                          <div className="space-y-1 max-h-24 overflow-y-auto">
                            {enhancedFingerprint.data.serverData.visitorHistory.visits.slice(0, 3).map((visit) => (
                              <div key={visit.requestId} className="text-xs text-gray-400 p-2 bg-gray-800/50 rounded">
                                <div className="flex justify-between items-center">
                                  <span>{safeRender(new Date(visit.timestamp * 1000).toLocaleString())}</span>
                                  <span className="text-blue-400">{safeRender(visit.ip)}</span>
                                </div>
                                {visit.browserDetails && (
                                  <div className="text-gray-500 mt-1">
                                    {safeRender(visit.browserDetails.browserName)} {safeRender(visit.browserDetails.browserVersion)}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-400">
                  Fingerprint Pro 数据加载中...
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
