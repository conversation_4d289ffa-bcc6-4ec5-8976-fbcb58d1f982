# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目架构

这是一个使用 Next.js 15 和 TypeScript 构建的现代 IP 检测应用，集成了 Fingerprint Pro 进行高级设备指纹识别和风险检测。

### 技术栈
- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS v4
- **组件库**: shadcn/ui 组件配置 (New York 风格)
- **主题**: next-themes 支持深色/浅色主题
- **指纹识别**: Fingerprint Pro React SDK
- **工具**: ESLint、Turbopack (开发模式)

### 核心功能模块
- **IP 检测**: 基于 Fingerprint Pro 的精准 IP 地理位置定位
- **设备指纹**: 浏览器指纹识别和设备信息收集
- **风险检测**: VPN、代理、Tor 检测和安全分析
- **网络诊断**: WebRTC 连接信息和网络质量评估
- **实时更新**: 支持数据刷新和动态状态更新

### 应用架构
- **客户端渲染**: 主要使用 `"use client"` 指令，基于客户端状态管理
- **API 路由**: 提供服务端 API 接口，处理 Fingerprint Pro 服务端调用
- **Hook 系统**: 大量自定义 Hook 封装各种功能逻辑
- **组件化**: 模块化组件设计，支持可重用性

### 目录结构
- `app/` - Next.js App Router 主要应用代码
  - `layout.tsx` - 根布局，包含主题和 Fingerprint Pro 配置
  - `page.tsx` - 主页面（重定向到 modern-page.tsx）
  - `modern-page.tsx` - 核心应用页面，包含所有功能
  - `api/` - API 路由
    - `ip-info/` - IP 信息 API
    - `fingerprint/` - Fingerprint Pro 相关 API
    - `http-headers/` - HTTP 头部信息 API
- `hooks/` - 自定义 React Hook
  - `use-enhanced-fingerprint.ts` - 增强版指纹数据管理
  - `use-device-info.ts` - 设备信息检测
  - `use-webrtc-info.ts` - WebRTC 网络信息
  - `use-browser-fingerprint.ts` - 浏览器指纹
  - 其他功能性 Hook
- `components/` - 可重用组件
  - `fingerprint-diagnostics.tsx` - 指纹诊断组件
  - `info-card.tsx` - 信息卡片组件
  - `theme-toggle.tsx` - 主题切换组件
  - 其他 UI 组件
- `lib/` - 共享工具函数
  - `utils.ts` - 包含 shadcn/ui 的 `cn` 样式合并函数
- `types/` - TypeScript 类型定义

## 开发命令

```bash
# 开发服务器 (使用 Turbopack)
npm run dev

# 生产构建
npm run build

# 生产服务器
npm run start

# 代码检查
npm run lint
```

## 环境配置

### 必需的环境变量
```env
NEXT_PUBLIC_FINGERPRINT_API_KEY=your_fingerprint_api_key
NEXT_PUBLIC_FINGERPRINT_REGION=us|eu|ap
FINGERPRINT_SECRET_KEY=your_fingerprint_secret_key
```

### Fingerprint Pro 区域配置
- `us` (默认): https://api.fpjs.io
- `eu`: https://eu.api.fpjs.io  
- `ap`: https://ap.api.fpjs.io

## 开发须知

### 样式系统
- 使用 Tailwind CSS v4 作为主要样式系统
- 配置了 shadcn/ui 组件库，使用 New York 风格
- 组件别名：`@/components`、`@/lib/utils`、`@/ui`、`@/hooks`
- 图标库：Lucide React
- 支持深色/浅色主题切换

### 路径别名
- `@/*` 指向项目根目录
- TypeScript 配置支持绝对路径导入

### 代码标准
- 使用 Next.js 和 TypeScript 的 ESLint 配置
- 严格的 TypeScript 设置
- 支持 JSX 和现代 ES 特性

### 字体配置
- 主字体：Geist Sans (`--font-geist-sans`)
- 等宽字体：Geist Mono (`--font-geist-mono`)

### 数据流架构
- **客户端数据**: 通过 Fingerprint Pro React SDK 获取
- **服务端数据**: 通过 API 路由调用 Fingerprint Pro 服务端 API
- **状态管理**: 使用 React Hook 进行状态管理，支持加载状态和错误处理
- **数据刷新**: 支持手动刷新和自动重新获取数据

### 错误处理
- 提供详细的错误信息和诊断功能
- 支持重试机制和降级处理
- 开发环境提供模拟数据支持

### 性能优化
- 使用 Next.js 15 的最新优化特性
- 组件懒加载和代码分割
- 缓存策略和数据预获取